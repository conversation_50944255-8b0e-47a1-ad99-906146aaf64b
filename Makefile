# PDF 语言检测工具 Makefile

.PHONY: help build test clean install docker release check format lint audit

# 默认目标
help: ## 显示帮助信息
	@echo "PDF 语言检测工具 - 可用命令："
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# 构建相关
build: ## 构建项目
	cargo build

build-release: ## 构建发布版本
	cargo build --release

build-all: smart-build ## 智能构建（推荐）

smart-build: ## 智能构建脚本（自动选择最佳方法）
	@echo "🤖 启动智能构建脚本..."
	./build.sh 4

interactive-build: ## 交互式构建选择
	@echo "🎯 交互式构建..."
	./build.sh

build-native-only: build-release ## 仅原生构建
	@echo "✅ 原生构建完成"
	@echo "二进制文件: target/release/rust-pdf-lang-detection"

build-cross-help: ## 显示交叉编译帮助信息
	@echo "🔧 交叉编译解决方案"
	@echo "=================="
	@echo ""
	@echo "❌ 问题: 缺少交叉编译工具链 (x86_64-linux-gnu-gcc)"
	@echo ""
	@echo "✅ 推荐解决方案:"
	@echo "1. 🐳 使用 Docker (最简单):"
	@echo "   make build-docker-native"
	@echo ""
	@echo "2. 📦 使用 GitHub Actions (CI/CD):"
	@echo "   推送代码到 GitHub，自动构建多平台"
	@echo ""
	@echo "3. 🛠️ 本地安装工具链 (复杂):"
	@echo "   # Ubuntu/Debian:"
	@echo "   sudo apt-get install gcc-x86-64-linux-gnu gcc-aarch64-linux-gnu"
	@echo "   sudo apt-get install libc6-dev-amd64-cross libc6-dev-arm64-cross"
	@echo ""
	@echo "   # macOS (需要额外配置):"
	@echo "   brew install x86_64-elf-gcc aarch64-elf-gcc"
	@echo ""
	@echo "4. 🎯 当前平台构建 (推荐):"
	@echo "   make build-release"

# 移除有问题的交叉编译目标，避免用户误用
build-cross-disabled: ## 交叉编译已禁用（避免工具链错误）
	@echo "❌ 本地交叉编译已禁用"
	@echo "原因: 需要复杂的工具链配置，容易出错"
	@echo ""
	@echo "请使用以下替代方案:"
	@echo "  make build-docker-native  # Docker 构建"
	@echo "  make build-cross-help     # 查看详细说明"

build-all-docker: build-docker-native ## 使用 Docker 构建 Linux 目标
	@echo "✅ Docker 构建完成"

build-docker-native: ## 在 Docker 中构建原生 Linux 二进制
	@echo "🐳 在 Docker 容器中构建原生 Linux 二进制..."
	docker run --rm -v $(PWD):/workspace -w /workspace \
		rust:1.89-slim sh -c '\
		apt-get update -qq && \
		apt-get install -y -qq pkg-config libpoppler-glib-dev libcairo2-dev libpango1.0-dev libgdk-pixbuf2.0-dev libglib2.0-dev && \
		echo "📦 依赖安装完成，开始构建..." && \
		cargo build --release && \
		echo "✅ 构建完成，二进制文件: target/release/rust-pdf-lang-detection"' || echo "❌ Docker 构建失败"

build-docker-multiarch: ## Docker 多架构构建（使用 buildx）
	@echo "🐳 使用 Docker buildx 构建多架构镜像..."
	@echo "这将构建 Docker 镜像而不是本地二进制文件"
	docker buildx create --use --name multiarch-builder 2>/dev/null || true
	docker buildx build --platform linux/amd64,linux/arm64 -t rust-pdf-lang-detection:latest . --load || \
	echo "❌ 多架构构建失败，可能需要启用 Docker buildx"

build-docker-extract: ## 从 Docker 镜像中提取二进制文件
	@echo "📦 从 Docker 镜像中提取二进制文件..."
	docker build -t rust-pdf-temp .
	docker create --name temp-container rust-pdf-temp
	docker cp temp-container:/usr/local/bin/rust-pdf-lang-detection ./rust-pdf-lang-detection-linux
	docker rm temp-container
	docker rmi rust-pdf-temp
	@echo "✅ Linux 二进制文件已提取: ./rust-pdf-lang-detection-linux"

# 测试相关
test: ## 运行测试
	cargo test

test-verbose: ## 运行详细测试
	cargo test -- --nocapture

test-coverage: ## 生成测试覆盖率报告
	cargo install cargo-llvm-cov --locked
	cargo llvm-cov --html

# 代码质量
check: ## 运行所有检查
	cargo check
	cargo clippy -- -D warnings
	cargo fmt -- --check

format: ## 格式化代码
	cargo fmt

lint: ## 运行 Clippy 检查
	cargo clippy -- -D warnings

audit: ## 安全审计
	cargo audit

deny: ## 运行 cargo-deny 检查
	cargo deny check

# 清理
clean: ## 清理构建文件
	cargo clean
	rm -rf target/
	rm -f *.tar.gz *.zip
	rm -rf dist/

# 安装相关
install-deps: ## 安装系统依赖 (Ubuntu/Debian)
	sudo apt-get update
	sudo apt-get install -y \
		libpoppler-glib-dev \
		libcairo2-dev \
		libpango1.0-dev \
		libgdk-pixbuf2.0-dev \
		libglib2.0-dev

install-deps-macos: ## 安装系统依赖 (macOS)
	brew install poppler cairo pango gdk-pixbuf glib

install-ocr: ## 安装 OCR 支持
	@if command -v apt-get >/dev/null 2>&1; then \
		sudo apt-get install -y ocrmypdf tesseract-ocr tesseract-ocr-chi-sim tesseract-ocr-chi-tra; \
	elif command -v brew >/dev/null 2>&1; then \
		brew install ocrmypdf tesseract tesseract-lang; \
	else \
		echo "请手动安装 ocrmypdf 和 tesseract"; \
	fi

install-tools: ## 安装开发工具
	cargo install cargo-audit
	cargo install cargo-deny
	cargo install cargo-llvm-cov
	cargo install cargo-outdated
	cargo install cargo-edit

install: build-release ## 安装到系统路径
	sudo cp target/release/rust-pdf-lang-detection /usr/local/bin/
	sudo chmod +x /usr/local/bin/rust-pdf-lang-detection

# Docker 相关
docker-build: ## 构建 Docker 镜像
	docker build -t rust-pdf-lang-detection .

docker-run: ## 运行 Docker 容器
	docker run --rm -v $(PWD)/test-pdfs:/app/input:ro -v $(PWD)/output:/app/output rust-pdf-lang-detection --input-dir /app/input --export-json /app/output/results.json

docker-compose-up: ## 使用 Docker Compose 启动
	docker-compose up --build

docker-compose-down: ## 停止 Docker Compose
	docker-compose down

# Docker 交叉编译
docker-build-linux-amd64: ## 使用 Docker 构建 Linux AMD64
	docker run --rm -v $(PWD):/workspace -w /workspace \
		rust:1.89-slim sh -c '\
		apt-get update && \
		apt-get install -y pkg-config libpoppler-glib-dev libcairo2-dev libpango1.0-dev libgdk-pixbuf2.0-dev libglib2.0-dev && \
		rustup target add x86_64-unknown-linux-gnu && \
		cargo build --release --target x86_64-unknown-linux-gnu'

docker-build-linux-arm64: ## 使用 Docker 构建 Linux ARM64
	docker run --rm -v $(PWD):/workspace -w /workspace --platform linux/arm64 \
		rust:1.89-slim sh -c '\
		apt-get update && \
		apt-get install -y pkg-config libpoppler-glib-dev libcairo2-dev libpango1.0-dev libgdk-pixbuf2.0-dev libglib2.0-dev && \
		rustup target add aarch64-unknown-linux-gnu && \
		cargo build --release --target aarch64-unknown-linux-gnu'

# 发布相关
package: build-release ## 打包发布文件
	mkdir -p dist
	cp target/release/rust-pdf-lang-detection dist/
	cp README.md QUICKSTART.md BINARY_RELEASES.md dist/
	cp install.sh test-installation.sh dist/
	cp -r examples dist/
	tar -czf rust-pdf-lang-detection-$(shell uname -s | tr '[:upper:]' '[:lower:]')-$(shell uname -m).tar.gz -C dist .

release-local: package ## 创建本地发布包
	@echo "发布包已创建: rust-pdf-lang-detection-$(shell uname -s | tr '[:upper:]' '[:lower:]')-$(shell uname -m).tar.gz"

# 开发相关
dev-setup: install-deps install-tools ## 设置开发环境
	@echo "开发环境设置完成"

dev-test: ## 运行开发测试
	mkdir -p test-pdfs output data
	cargo run -- --input-dir ./test-pdfs --export-json ./output/dev-test-results.json --max-concurrent 1

benchmark: build-release ## 运行性能基准测试
	@echo "运行性能基准测试..."
	mkdir -p benchmark-results
	time ./target/release/rust-pdf-lang-detection --input-dir ./test-pdfs --export-json ./benchmark-results/benchmark.json

# 文档相关
docs: ## 生成文档
	cargo doc --open

docs-build: ## 构建文档
	cargo doc --no-deps

# 更新相关
update: ## 更新依赖
	cargo update

upgrade: ## 升级依赖到最新版本
	cargo install cargo-edit
	cargo upgrade

# CI/CD 相关
ci-test: ## 运行 CI 测试
	cargo test --verbose
	cargo clippy -- -D warnings
	cargo fmt -- --check

ci-build: ## 运行 CI 构建
	cargo build --release

# 实用工具
size: build-release ## 显示二进制文件大小
	@echo "二进制文件大小："
	@ls -lh target/release/rust-pdf-lang-detection
	@echo ""
	@echo "压缩后大小："
	@strip target/release/rust-pdf-lang-detection
	@ls -lh target/release/rust-pdf-lang-detection

deps-tree: ## 显示依赖树
	cargo tree

deps-outdated: ## 检查过时的依赖
	cargo outdated

version: ## 显示版本信息
	@echo "Rust 版本："
	@rustc --version
	@echo "Cargo 版本："
	@cargo --version
	@if [ -f target/release/rust-pdf-lang-detection ]; then \
		echo "程序版本："; \
		./target/release/rust-pdf-lang-detection --version; \
	fi

# 快速命令
quick-test: ## 快速测试（仅编译检查）
	cargo check

quick-build: ## 快速构建（debug 模式）
	cargo build

all: clean check test build-release package ## 运行完整的构建流程

# 状态和信息
status: ## 显示构建状态和可用二进制文件
	@echo "📊 Rust PDF 语言检测工具构建状态"
	@echo "=================================="
	@echo "当前平台: $(shell rustc -vV | grep host | cut -d' ' -f2)"
	@echo "Rust 版本: $(shell rustc --version)"
	@echo "Cargo 版本: $(shell cargo --version)"
	@echo ""
	@echo "可用二进制文件:"
	@ls -la target/release/rust-pdf-lang-detection 2>/dev/null || echo "  未找到发布版本。运行 'make build-release' 构建。"
	@ls -la target/*/rust-pdf-lang-detection 2>/dev/null | grep -v release || true
	@echo ""
	@echo "构建目标:"
	@echo "  make build-release     - 构建当前平台（完整功能）"
	@echo "  make build-all         - 构建原生 + Docker Linux"
	@echo "  make build-all-docker  - Docker 交叉编译"
	@echo "  make docker-build      - 构建 Docker 镜像"

build-info: ## 显示构建信息和解决方案
	@echo "🔧 Rust PDF 语言检测工具构建指南"
	@echo "================================="
	@echo ""
	@echo "🎯 推荐构建方式 (已解决交叉编译问题):"
	@echo "   make build-all           # 智能构建 (自动选择最佳方法)"
	@echo "   ./build.sh              # 交互式构建选择"
	@echo "   make build-release       # 原生构建"
	@echo ""
	@echo "🤖 智能构建特性:"
	@echo "   • 自动检测环境 (OS, 架构, Docker, Rust)"
	@echo "   • 智能选择构建方法"
	@echo "   • 友好的错误提示和解决建议"
	@echo "   • 避免交叉编译工具链问题"
	@echo ""
	@echo "🐳 Docker 构建 (Linux 部署):"
	@echo "   make build-docker-native  # Docker 中构建 Linux 版本"
	@echo "   make docker-build        # 构建 Docker 镜像"
	@echo "   make build-docker-extract # 从镜像提取二进制"
	@echo ""
	@echo "📊 状态和帮助:"
	@echo "   make status              # 查看构建状态"
	@echo "   make build-cross-help    # 交叉编译解决方案"
	@echo "   make help               # 查看所有命令"
	@echo ""
	@echo "✅ 问题解决:"
	@echo "   • ❌ 交叉编译工具链错误 → ✅ 智能构建脚本"
	@echo "   • ❌ pkg-config 配置复杂 → ✅ Docker 原生构建"
	@echo "   • ❌ 平台兼容性问题 → ✅ 自动环境检测"
