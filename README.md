# PDF 语言检测工具

一个高性能的 PDF 文档语言检测工具，支持批量处理和 OCR 文本提取。能够自动识别 PDF 文档的主要语言，并提供详细的检测结果。

## ✨ 主要特性

- 🚀 **高性能批量处理**：支持并发处理大量 PDF 文件
- 🔍 **智能语言检测**：使用 whichlang 库进行准确的语言识别
- 📄 **OCR 支持**：当 PDF 无法直接提取文本时，自动使用 OCR 进行文本识别
- 💾 **数据持久化**：使用 SQLite 数据库存储检测结果
- 📊 **详细统计**：提供语言分布和检测质量统计
- 📤 **多格式导出**：支持 JSON 格式导出结果
- ⚡ **内存优化**：支持内存限制和页数限制，适合处理大文件

## 🛠️ 系统要求

### 基础要求
- **操作系统**: Linux, macOS, Windows
- **架构**: x86_64, ARM64

### 运行时依赖

#### Linux (Ubuntu/Debian)
```bash
# 安装运行时依赖
sudo apt-get update
sudo apt-get install -y \
    libpoppler-glib8 \
    libcairo2 \
    libpango-1.0-0 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0

# 安装 OCR 支持（可选）
sudo apt-get install -y ocrmypdf tesseract-ocr tesseract-ocr-chi-sim tesseract-ocr-chi-tra
```

#### macOS
```bash
# 使用 Homebrew 安装运行时依赖
brew install poppler cairo pango gdk-pixbuf glib

# 安装 OCR 支持（可选）
brew install ocrmypdf tesseract tesseract-lang
```

#### CentOS/RHEL/Fedora
```bash
# CentOS/RHEL
sudo yum install -y \
    poppler-glib \
    cairo \
    pango \
    gdk-pixbuf2 \
    glib2

# Fedora
sudo dnf install -y \
    poppler-glib \
    cairo \
    pango \
    gdk-pixbuf2 \
    glib2

# 安装 OCR 支持（可选）
# CentOS/RHEL: 需要 EPEL 仓库
sudo yum install -y epel-release
sudo yum install -y ocrmypdf tesseract

# Fedora
sudo dnf install -y ocrmypdf tesseract
```

## 📦 安装

### 方法 1：下载预编译二进制文件（推荐）

1. **从 Releases 页面下载**
```bash
# 下载适合您系统的二进制文件
# Linux x86_64
wget https://github.com/CheinTian/rust-pdf-lang-detector/releases/latest/download/rust-pdf-lang-detection-linux-x86_64.tar.gz
tar -xzf rust-pdf-lang-detection-linux-x86_64.tar.gz

# macOS x86_64
wget https://github.com/CheinTian/rust-pdf-lang-detector/releases/latest/download/rust-pdf-lang-detection-macos-x86_64.tar.gz
tar -xzf rust-pdf-lang-detection-macos-x86_64.tar.gz

# macOS ARM64 (Apple Silicon)
wget https://github.com/CheinTian/rust-pdf-lang-detector/releases/latest/download/rust-pdf-lang-detection-macos-arm64.tar.gz
tar -xzf rust-pdf-lang-detection-macos-arm64.tar.gz
```

2. **安装到系统路径**
```bash
# 复制到系统路径（可选）
sudo cp rust-pdf-lang-detection /usr/local/bin/
sudo chmod +x /usr/local/bin/rust-pdf-lang-detection
```

### 方法 2：使用安装脚本

```bash
# 下载并运行安装脚本
curl -sSL https://raw.githubusercontent.com/CheinTian/rust-pdf-lang-detector/main/install.sh | bash
```

### 方法 3：从源码编译（开发者）

如果您需要修改代码或贡献开发：

1. **安装 Rust 开发环境**
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

2. **克隆仓库并编译**
```bash
git clone <repository-url>
cd rust-pdf-lang-detection
cargo build --release
```

## 🚀 使用方法

### 基本用法

```bash
# 使用下载的二进制文件
./rust-pdf-lang-detection --input-dir /path/to/pdf/directory

# 如果安装到系统路径
rust-pdf-lang-detection --input-dir /path/to/pdf/directory

# 开发者从源码运行
cargo run -- --input-dir /path/to/pdf/directory
```

### 常用参数

```bash
# 基本处理
./rust-pdf-lang-detection --input-dir /path/to/pdfs

# 启用 OCR 支持
./rust-pdf-lang-detection --input-dir /path/to/pdfs --enable-ocr true

# 设置并发数和批次大小
./rust-pdf-lang-detection --input-dir /path/to/pdfs --max-concurrent 4 --batch-size 10

# 限制处理页数和内存使用
./rust-pdf-lang-detection --input-dir /path/to/pdfs --max-pages 10 --memory-limit 512

# 导出结果到 JSON 文件
./rust-pdf-lang-detection --input-dir /path/to/pdfs --export-json results.json

# 跳过已处理的文件
./rust-pdf-lang-detection --input-dir /path/to/pdfs --skip-processed true
```

### 完整参数列表

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--input-dir` | 路径 | 必需 | 包含 PDF 文件的输入目录 |
| `--db-path` | 路径 | `./data/pdf_languages.db` | SQLite 数据库文件路径 |
| `--max-concurrent` | 数字 | `4` | 最大并发处理文件数 |
| `--batch-size` | 数字 | `10` | 批处理大小 |
| `--skip-processed` | 布尔 | `true` | 是否跳过已处理的文件 |
| `--export-json` | 路径 | 可选 | JSON 结果导出文件路径 |
| `--max-pages` | 数字 | `50` | 每个 PDF 处理的最大页数 |
| `--memory-limit` | 数字 | `256` | 内存限制（MB） |
| `--enable-ocr` | 布尔 | `true` | 是否启用 OCR 功能 |
| `--ocr-languages` | 字符串 | `eng+chi_sim+chi_tra` | OCR 语言设置 |

## 📊 输出格式

### JSON 导出格式

```json
[
  {
    "filename": "document.pdf",
    "language": "eng",
    "confidence": 0.9,
    "text_length": 3327,
    "text_sample": "Sample text from the document...",
    "detection_method": "whichlang",
    "processed_at": "2025-08-11 09:58:24",
    "error": null
  }
]
```

### 字段说明

- `filename`: PDF 文件名
- `language`: 检测到的语言代码（如 eng, cmn, kor, jpn 等）
- `confidence`: 检测置信度（0.0-1.0）
- `text_length`: 提取的文本长度
- `text_sample`: 文本样本（限制 200 字符）
- `detection_method`: 检测方法（whichlang 或 ocr）
- `processed_at`: 处理时间戳
- `error`: 错误信息（如果有）

## 🔧 OCR 配置

### 支持的语言

OCR 功能支持多种语言，常用语言代码：

- `eng`: 英文
- `chi_sim`: 简体中文
- `chi_tra`: 繁体中文
- `kor`: 韩文
- `jpn`: 日文
- `fra`: 法文
- `deu`: 德文
- `spa`: 西班牙文

### 安装额外语言包

#### Ubuntu/Debian
```bash
sudo apt-get install tesseract-ocr-[语言代码]
# 例如：
sudo apt-get install tesseract-ocr-chi-sim tesseract-ocr-jpn
```

#### macOS
```bash
# tesseract-lang 包含了大部分语言
brew install tesseract-lang
```

### 查看可用语言
```bash
tesseract --list-langs
```

## 📈 性能优化

### 推荐配置

**小规模处理（< 100 文件）：**
```bash
cargo run -- --input-dir /path/to/pdfs --max-concurrent 2 --batch-size 5
```

**中等规模处理（100-1000 文件）：**
```bash
cargo run -- --input-dir /path/to/pdfs --max-concurrent 4 --batch-size 10 --memory-limit 512
```

**大规模处理（> 1000 文件）：**
```bash
cargo run -- --input-dir /path/to/pdfs --max-concurrent 8 --batch-size 20 --memory-limit 1024 --max-pages 20
```

### 性能调优建议

1. **并发数设置**：建议设置为 CPU 核心数的 1-2 倍
2. **内存限制**：根据系统内存设置，避免 OOM
3. **页数限制**：对于大文件，限制处理页数可显著提升速度
4. **批次大小**：较大的批次可以减少数据库写入次数

## 🐛 故障排除

### 常见问题

**1. 编译错误：找不到 poppler 库**
```bash
# Ubuntu/Debian
sudo apt-get install libpoppler-glib-dev

# macOS
brew install poppler
```

**2. OCR 功能不可用**
```bash
# 检查 ocrmypdf 是否安装
ocrmypdf --version

# 安装 ocrmypdf
pip install ocrmypdf
# 或
brew install ocrmypdf  # macOS
```

**3. 权限错误**
```bash
# 确保对输入目录和数据库目录有读写权限
chmod 755 /path/to/pdf/directory
mkdir -p ./data && chmod 755 ./data
```

**4. 内存不足**
```bash
# 降低并发数和内存限制
cargo run -- --input-dir /path/to/pdfs --max-concurrent 1 --memory-limit 128
```

### 调试模式

启用详细日志：
```bash
RUST_LOG=debug cargo run -- --input-dir /path/to/pdfs
```

## 📝 示例

### 处理专利文档
```bash
# 处理专利文档，启用中英韩 OCR
cargo run -- \
    --input-dir ./patents \
    --enable-ocr true \
    --ocr-languages "eng+chi_sim+chi_tra+kor" \
    --max-pages 5 \
    --export-json patent_results.json
```

### 快速扫描
```bash
# 快速扫描，只处理前 3 页
cargo run -- \
    --input-dir ./documents \
    --max-pages 3 \
    --max-concurrent 8 \
    --enable-ocr false
```

### 生产环境处理
```bash
# 生产环境配置
./target/release/rust-pdf-lang-detection \
    --input-dir /data/pdfs \
    --db-path /data/results.db \
    --max-concurrent 6 \
    --batch-size 15 \
    --memory-limit 1024 \
    --export-json /data/results.json \
    --skip-processed true
```

## 📄 许可证

[在此添加许可证信息]

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 🏗️ 架构说明

### 核心组件

1. **PDF 文本提取**：使用 poppler-rs 库直接从 PDF 提取文本
2. **OCR 回退机制**：当直接提取失败时，使用 ocrmypdf 进行 OCR 识别
3. **语言检测**：使用 whichlang 库进行高精度语言识别
4. **数据存储**：SQLite 数据库存储所有检测结果
5. **并发处理**：基于 Tokio 的异步并发处理框架

### 处理流程

```
PDF 文件 → 文本提取 → 语言检测 → 数据库存储
    ↓           ↓
  失败时    OCR 处理 → 语言检测 → 数据库存储
```

## 🔍 支持的语言

该工具可以检测 100+ 种语言，包括但不限于：

| 语言 | 代码 | 语言 | 代码 | 语言 | 代码 |
|------|------|------|------|------|------|
| 英语 | eng | 中文 | cmn | 日语 | jpn |
| 韩语 | kor | 法语 | fra | 德语 | deu |
| 西班牙语 | spa | 俄语 | rus | 阿拉伯语 | ara |
| 葡萄牙语 | por | 意大利语 | ita | 荷兰语 | nld |

完整的语言列表请参考 [whichlang 文档](https://github.com/greyblake/whichlang-rs)。

## 📊 性能基准

在标准配置下的性能表现：

| 文件类型 | 平均处理时间 | OCR 处理时间 | 内存使用 |
|----------|--------------|--------------|----------|
| 纯文本 PDF (10页) | 0.1-0.5秒 | N/A | 10-20MB |
| 扫描版 PDF (10页) | 3-8秒 | 2-6秒 | 50-100MB |
| 大文件 PDF (50页) | 2-10秒 | 10-30秒 | 100-200MB |

*测试环境：Intel i7-8核，16GB RAM，SSD 存储*

## 🔐 安全注意事项

1. **文件权限**：确保程序对输入目录有读权限，对数据库目录有写权限
2. **临时文件**：OCR 处理会创建临时文件，程序会自动清理
3. **内存使用**：大文件处理时注意内存限制设置
4. **并发限制**：避免设置过高的并发数，可能导致系统资源耗尽

## 📞 支持

如有问题，请提交 Issue 或联系维护者。

### 常用链接

- [项目仓库](https://github.com/CheinTian/rust-pdf-lang-detector)
- [问题反馈](https://github.com/CheinTian/rust-pdf-lang-detector/issues)
- [功能请求](https://github.com/CheinTian/rust-pdf-lang-detector/issues/new?template=feature_request.md)
