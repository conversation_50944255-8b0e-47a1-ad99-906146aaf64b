[package]
name = "rust-pdf-lang-detection"
version = "0.1.0"
edition = "2024"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
poppler-rs = "0.25.0"
whichlang = "0.1.1"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
sqlx = { version = "0.7", features = ["sqlite", "runtime-tokio-rustls"] }
walkdir = "2.3"
indicatif = "0.17"
clap = { version = "4.0", features = ["derive"] }
# OCR 相关依赖
ocrmypdf-rs = "0.0.8"

glib-sys = { version = "0.21.1", features = ["v2_58"], default-features = false }
