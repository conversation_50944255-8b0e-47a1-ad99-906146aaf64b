use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::sync::Semaphore;
use tokio::task;
use serde::{Deserialize, Serialize};
use anyhow::{Result, Context};
use tracing::{info, warn, error, debug};
use sqlx::{Pool, Sqlite, Row};
use clap::Parser;
use poppler::Document;
use ocrmypdf_rs::{OcrMyPdf, Ocr};

// 系统依赖安装说明:
/*
# Ubuntu/Debian:
sudo apt-get install libpoppler-glib-dev pkg-config

# CentOS/RHEL/Fedora:
sudo yum install poppler-glib-devel pkgconfig
# 或者: sudo dnf install poppler-glib-devel pkgconfig

# macOS:
brew install poppler pkg-config

# Windows:
# 需要安装 MSYS2 和相关的 poppler 包，或使用预编译版本
*/



#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
struct Args {
    /// PDF文件目录路径
    #[arg(short, long)]
    input_dir: PathBuf,

    /// 数据库文件路径
    #[arg(short, long, default_value = "./data/pdf_languages.db")]
    database: String,

    /// 最大并发文件数
    #[arg(short = 'c', long, default_value_t = 16)]
    max_concurrent: usize,

    /// 批次大小
    #[arg(short, long, default_value_t = 500)]
    batch_size: usize,

    /// 是否跳过已处理的文件 (true/false)
    #[arg(long, default_value = "true", action = clap::ArgAction::Set)]
    skip_processed: bool,

    /// 导出结果到JSON文件
    #[arg(short, long)]
    export_json: Option<PathBuf>,

    /// 每个PDF文档处理的最大页数
    #[arg(long, default_value_t = 50)]
    max_pages: usize,

    /// 内存使用限制（MB）
    #[arg(long, default_value_t = 256)]
    memory_limit: usize,

    /// 是否启用 OCR 功能
    #[arg(long, default_value = "true", action = clap::ArgAction::Set)]
    enable_ocr: bool,

    /// OCR 语言设置 (如: eng, chi_sim, chi_tra, kor, jpn)
    #[arg(long, default_value = "eng+chi_sim+chi_tra")]
    ocr_languages: String,
}

// JSON导出用的简化结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LanguageDetection {
    pub filename: String,           // 文件名（不含路径）
    pub language: String,           // 检测到的语言
    pub confidence: f64,            // 检测置信度
    pub text_length: usize,         // 提取的文本长度
    pub text_sample: String,        // 文本样本（限制长度）
    pub detection_method: String,   // 检测方法：whichlang/ocr
    pub processed_at: String,       // 处理时间戳
    pub error: Option<String>,      // 错误信息
}

// 内部处理用的完整结构体
#[derive(Debug, Clone)]
pub struct InternalDetectionResult {
    pub file_path: String,
    pub primary_language: String,
    pub confidence: f64,
    pub text_sample: String,
    pub file_size: u64,
    pub processing_time_ms: u64,
    pub error: Option<String>,
    pub detection_method: String,
    pub text_length: usize,
    pub is_reliable: bool,
}

#[derive(Debug, Clone)]
pub struct ProcessingConfig {
    pub max_concurrent_files: usize,
    pub max_text_extract_length: usize,
    pub min_reliable_text_length: usize,  // 可靠检测的最小文本长度
    pub batch_size: usize,
    pub database_path: String,
    pub skip_processed: bool,
    pub use_multiple_detectors: bool,     // 是否使用多个检测器
    pub max_pages_per_document: Option<usize>, // 限制每个文档处理的最大页数
    pub memory_limit_mb: usize,           // 内存使用限制（MB）
    pub enable_ocr: bool,                 // 是否启用 OCR 功能
    pub ocr_languages: String,            // OCR 语言设置
}

impl Default for ProcessingConfig {
    fn default() -> Self {
        Self {
            max_concurrent_files: 16,
            max_text_extract_length: 10000,
            min_reliable_text_length: 300,
            batch_size: 1000,
            database_path: "./data/pdf_languages.db".to_string(),
            skip_processed: true,
            use_multiple_detectors: true,
            max_pages_per_document: Some(100), // 默认最多处理100页
            memory_limit_mb: 512, // 默认512MB内存限制
            enable_ocr: true, // 默认启用 OCR
            ocr_languages: "eng+chi_sim+chi_tra".to_string(), // 默认支持英文和中文
        }
    }
}

pub struct PdfLanguageDetector {
    config: ProcessingConfig,
    db_pool: Pool<Sqlite>,
    semaphore: Arc<Semaphore>,
}

impl PdfLanguageDetector {
    pub async fn new(config: ProcessingConfig) -> Result<Self> {
        Self::initialize_database(&config).await?;

        // 初始化数据库连接池
        let db_url = format!("sqlite:{}?mode=rwc", config.database_path);
        let db_pool = sqlx::sqlite::SqlitePoolOptions::new()
            .max_connections(10)
            .connect(&db_url)
            .await
            .context("Failed to connect to database")?;

        // 创建表结构
        Self::create_tables(&db_pool).await?;

        let semaphore = Arc::new(Semaphore::new(config.max_concurrent_files));

        Ok(Self {
            config,
            db_pool,
            semaphore,
        })
    }

    async fn initialize_database(config: &ProcessingConfig) -> Result<()> {
        let db_path = Path::new(&config.database_path);

        // 确保数据库文件的目录存在
        if let Some(parent) = db_path.parent() {
            if !parent.exists() {
                info!("创建数据库目录: {}", parent.display());
                std::fs::create_dir_all(parent)
                    .context("Failed to create database directory")?;
            }
        }

        // 检查目录权限
        let parent_dir = db_path.parent().unwrap_or_else(|| Path::new("."));
        if !parent_dir.exists() {
            return Err(anyhow::anyhow!("数据库目录不存在: {}", parent_dir.display()));
        }

        // 尝试创建一个测试文件来检查写权限
        let test_file = parent_dir.join(".write_test");
        match std::fs::write(&test_file, "test") {
            Ok(_) => {
                let _ = std::fs::remove_file(&test_file);
                info!("数据库目录写权限检查通过: {}", parent_dir.display());
            }
            Err(e) => {
                return Err(anyhow::anyhow!(
                    "数据库目录没有写权限 {}: {}",
                    parent_dir.display(),
                    e
                ));
            }
        }

        info!("数据库将创建在: {}", db_path.display());
        Ok(())
    }

    async fn create_tables(db_pool: &Pool<Sqlite>) -> Result<()> {
        // 创建表结构
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS pdf_languages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_path TEXT UNIQUE NOT NULL,
                primary_language TEXT NOT NULL,
                confidence REAL NOT NULL,
                text_sample TEXT,
                file_size INTEGER NOT NULL,
                processing_time_ms INTEGER NOT NULL,
                error TEXT,
                detection_method TEXT,
                text_length INTEGER,
                is_reliable BOOLEAN,
                processed_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
            .execute(db_pool)
            .await
            .context("Failed to create database table")?;

        // 创建索引提高查询性能
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_file_path ON pdf_languages(file_path)")
            .execute(db_pool)
            .await?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_language ON pdf_languages(primary_language)")
            .execute(db_pool)
            .await?;

        info!("数据库表和索引创建成功");
        Ok(())
    }

    pub async fn process_directory(&self, directory: &Path) -> Result<()> {
        info!("开始处理目录: {}", directory.display());

        // 获取所有PDF文件
        let pdf_files = self.collect_pdf_files(directory)?;
        info!("发现 {} 个PDF文件", pdf_files.len());

        // 使用进度条
        let progress_bar = indicatif::ProgressBar::new(pdf_files.len() as u64);
        progress_bar.set_style(
            indicatif::ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({eta})")
                .unwrap()
        );

        // 分批处理文件
        for chunk in pdf_files.chunks(self.config.batch_size) {
            let mut tasks = Vec::new();

            for file_path in chunk {
                // 检查是否已处理过
                if self.config.skip_processed && self.is_already_processed(file_path).await? {
                    progress_bar.inc(1);
                    continue;
                }

                let file_path = file_path.clone();
                let detector = self.clone();
                let pb = progress_bar.clone();

                let task = task::spawn(async move {
                    let result = detector.process_single_file(&file_path).await;
                    pb.inc(1);
                    result
                });

                tasks.push(task);
            }

            // 等待当前批次完成
            let results: Vec<Result<InternalDetectionResult>> = {
                let mut all_results = Vec::new();
                for task in tasks {
                    match task.await {
                        Ok(result) => all_results.push(result),
                        Err(e) => all_results.push(Err(anyhow::anyhow!("Task join error: {}", e))),
                    }
                }
                all_results
            };

            // 批量保存结果
            self.save_batch_results(results).await?;
        }

        progress_bar.finish_with_message("处理完成");
        Ok(())
    }

    async fn process_single_file(&self, file_path: &Path) -> Result<InternalDetectionResult> {
        let _permit = self.semaphore.acquire().await.unwrap();
        let start_time = std::time::Instant::now();

        let mut result = InternalDetectionResult {
            file_path: file_path.to_string_lossy().to_string(),
            primary_language: "unknown".to_string(),
            confidence: 0.0,
            text_sample: String::new(),
            file_size: 0,
            processing_time_ms: 0,
            error: None,
            detection_method: "none".to_string(),
            text_length: 0,
            is_reliable: false,
        };

        // 获取文件大小
        match fs::metadata(file_path) {
            Ok(metadata) => result.file_size = metadata.len(),
            Err(e) => {
                result.error = Some(format!("无法获取文件元数据: {}", e));
                result.processing_time_ms = start_time.elapsed().as_millis() as u64;
                return Ok(result);
            }
        }

        // 提取PDF文本
        let raw_text = match self.extract_pdf_text(file_path).await {
            Ok(text) => {
                info!("PDF文本提取成功: {} (原始长度: {} 字符)",
                      file_path.file_name().unwrap_or_default().to_string_lossy(),
                      text.chars().count());
                text
            },
            Err(e) => {
                warn!("PDF文本提取失败: {}, 尝试使用 OCR", e);

                // 尝试使用 OCR 作为回退方案
                match self.extract_text_with_ocr(file_path).await {
                    Ok(ocr_text) => {
                        info!("OCR 文本提取成功: {} (长度: {} 字符)",
                              file_path.file_name().unwrap_or_default().to_string_lossy(),
                              ocr_text.chars().count());
                        result.detection_method = "ocr".to_string();
                        ocr_text
                    },
                    Err(ocr_e) => {
                        result.error = Some(format!("PDF文本提取失败: {}; OCR也失败: {}", e, ocr_e));
                        result.processing_time_ms = start_time.elapsed().as_millis() as u64;

                        warn!("文件 {} PDF和OCR文本提取都失败: PDF错误: {}, OCR错误: {}",
                              file_path.display(), e, ocr_e);

                        return Ok(result);
                    }
                }
            }
        };

        // 清理和处理提取的文本
        let cleaned_text = clean_extracted_text(&raw_text);
        result.text_length = cleaned_text.chars().count();

        info!("文件 {} 提取文本长度: {} 字符",
              file_path.file_name().unwrap_or_default().to_string_lossy(),
              result.text_length);

        // 检测语言
        if !cleaned_text.trim().is_empty() && result.text_length >= 10 {
            // 根据文本长度采用不同的检测策略
            let detection_result = if result.text_length >= self.config.min_reliable_text_length {
                // 文本足够长，使用标准检测
                let sample = safe_substring(&cleaned_text, self.config.max_text_extract_length);
                detect_language_smart(&sample)
            } else {
                // 文本较短，尝试多个片段
                warn!("文本较短({} 字符)，使用增强检测", result.text_length);
                let samples = extract_text_samples(&cleaned_text,
                                                   std::cmp::min(result.text_length, 500), 3);

                let mut best_result: Option<DetectionResult> = None;
                for (i, sample) in samples.iter().enumerate() {
                    debug!("检测片段 {}: {}", i + 1, safe_substring(sample, 50));
                    if let Some(detection) = detect_language_smart(sample) {
                        if best_result.is_none() ||
                            (detection.confidence > best_result.as_ref().unwrap().confidence) {
                            best_result = Some(detection);
                        }
                    }
                }
                best_result
            };

            match detection_result {
                Some(detection) => {
                    result.primary_language = detection.language;
                    result.confidence = detection.confidence;
                    result.detection_method = detection.method;
                    result.is_reliable = detection.is_reliable;

                    // 保存文本样本
                    result.text_sample = safe_substring(&cleaned_text, 500);

                    if !result.is_reliable {
                        warn!("语言检测基于短文本，可能不够准确: {} (文本长度: {})",
                              result.primary_language, result.text_length);
                    } else {
                        info!("检测到语言: {} (文本长度: {})",
                              result.primary_language, result.text_length);
                    }
                }
                None => {
                    result.error = Some("无法识别文本语言".to_string());
                    warn!("无法识别文件 {} 的语言",
                          file_path.file_name().unwrap_or_default().to_string_lossy());
                }
            }
        } else {
            result.error = Some(format!("文本太短或为空 (长度: {})", result.text_length));
        }

        result.processing_time_ms = start_time.elapsed().as_millis() as u64;
        Ok(result)
    }

    async fn extract_pdf_text(&self, file_path: &Path) -> Result<String> {
        // 使用 tokio::task::spawn_blocking 来处理CPU密集型的PDF解析
        let file_path = file_path.to_owned();
        let config = self.config.clone();

        let result = task::spawn_blocking(move || {
            // 首先检查文件是否可读
            if !file_path.exists() {
                return Err(anyhow::anyhow!("PDF文件不存在: {}", file_path.display()));
            }

            let metadata = std::fs::metadata(&file_path)
                .with_context(|| format!("无法读取文件元数据: {}", file_path.display()))?;

            if metadata.len() == 0 {
                return Err(anyhow::anyhow!("PDF文件为空: {}", file_path.display()));
            }

            // 检查文件大小是否过大（超过100MB可能会导致内存问题）
            const MAX_FILE_SIZE: u64 = 100 * 1024 * 1024; // 100MB
            if metadata.len() > MAX_FILE_SIZE {
                warn!("PDF文件较大 ({:.1} MB)，处理可能较慢: {}",
                      metadata.len() as f64 / (1024.0 * 1024.0),
                      file_path.display());
            }

            debug!("开始提取PDF文本: {} (大小: {} 字节)",
                   file_path.display(), metadata.len());

            // 尝试提取文本
            let file_uri = format!("file://{}", file_path.to_string_lossy());
            debug!("PDF文件URI: {}", file_uri);

            match Document::from_file(&file_uri, None) {
                Ok(document) => {
                    let mut extracted_text = String::new();
                    let page_count = document.n_pages();

                    if page_count == 0 {
                        return Err(anyhow::anyhow!("PDF文档没有页面"));
                    }

                    // 获取文档的基本信息
                    let title = document.title().map(|s| s.to_string()).unwrap_or_else(|| "无标题".to_string());
                    let author = document.author().map(|s| s.to_string()).unwrap_or_else(|| "无作者".to_string());
                    let creator = document.creator().map(|s| s.to_string()).unwrap_or_else(|| "无创建者".to_string());

                    debug!("PDF文档信息: 页数={}, 标题='{}', 作者='{}', 创建者='{}'",
                           page_count, title, author, creator);

                    let mut successful_pages = 0;
                    let mut failed_pages = 0;

                    // 确定要处理的页数（考虑配置限制）
                    let max_pages = if let Some(max_pages_config) = config.max_pages_per_document {
                        std::cmp::min(page_count, max_pages_config as i32)
                    } else {
                        page_count
                    };

                    if max_pages < page_count {
                        info!("PDF文档有 {} 页，但只处理前 {} 页", page_count, max_pages);
                    }

                    // 遍历页面提取文本（带内存控制）
                    for page_num in 0..max_pages {
                        // 检查内存使用情况
                        let current_text_size = extracted_text.len();
                        let memory_limit_bytes = config.memory_limit_mb * 1024 * 1024;

                        if current_text_size > memory_limit_bytes {
                            warn!("文本提取达到内存限制 ({} MB)，停止处理剩余页面",
                                  config.memory_limit_mb);
                            break;
                        }

                        match document.page(page_num) {
                            Some(page) => {
                                debug!("正在处理页面 {}/{}", page_num + 1, max_pages);

                                // 获取页面尺寸信息
                                let (width, height) = page.size();
                                debug!("页面 {} 尺寸: {:.1} x {:.1}", page_num + 1, width, height);

                                match page.text() {
                                    Some(page_text) => {
                                        let page_text_str = page_text.to_string();
                                        let page_text_len = page_text_str.len();
                                        let page_char_count = page_text_str.chars().count();

                                        debug!("页面 {} 提取文本: {} 字节, {} 字符",
                                               page_num + 1, page_text_len, page_char_count);

                                        if !page_text_str.trim().is_empty() {
                                            // 显示页面文本的前100个字符用于调试
                                            let preview = if page_char_count > 100 {
                                                format!("{}...", page_text_str.chars().take(100).collect::<String>())
                                            } else {
                                                page_text_str.clone()
                                            };
                                            debug!("页面 {} 文本预览: {}", page_num + 1,
                                                   preview.replace('\n', " ").replace('\r', " "));

                                            // 检查单页文本大小是否合理
                                            if page_text_str.len() > 1024 * 1024 { // 1MB per page limit
                                                warn!("页面 {} 文本过大 ({} 字节)，截取前1MB",
                                                      page_num + 1, page_text_str.len());
                                                let truncated: String = page_text_str.chars().take(1024 * 512).collect(); // 约1MB
                                                extracted_text.push_str(&truncated);
                                            } else {
                                                extracted_text.push_str(&page_text_str);
                                            }
                                            extracted_text.push('\n'); // 页面间添加换行
                                            successful_pages += 1;
                                        } else {
                                            debug!("页面 {} 文本为空（去除空白后）", page_num + 1);
                                            failed_pages += 1;
                                        }
                                    }
                                    None => {
                                        debug!("页面 {} 的 text() 方法返回 None", page_num + 1);
                                        failed_pages += 1;
                                    }
                                }
                            }
                            None => {
                                warn!("document.page({}) 返回 None", page_num);
                                failed_pages += 1;
                            }
                        }
                    }

                    let char_count = extracted_text.chars().count();
                    let byte_count = extracted_text.len();

                    debug!("PDF文本提取完成: {} 字符, {} 字节 (成功页面: {}/{}, 失败页面: {})",
                           char_count, byte_count, successful_pages, page_count, failed_pages);

                    if extracted_text.trim().is_empty() {
                        if failed_pages == page_count {
                            return Err(anyhow::anyhow!("所有页面都无法提取文本，可能是扫描版PDF或加密PDF"));
                        } else {
                            return Err(anyhow::anyhow!("PDF文本为空，可能是扫描版PDF或加密PDF"));
                        }
                    }

                    // 如果大部分页面提取失败，给出警告
                    if failed_pages > successful_pages {
                        warn!("PDF文档 {} 中有 {}/{} 页面提取失败，可能包含扫描内容",
                              file_path.display(), failed_pages, page_count);
                    }

                    // 显示文本开头用于调试
                    let preview = if char_count > 200 {
                        format!("{}...", extracted_text.chars().take(200).collect::<String>())
                    } else {
                        extracted_text.clone()
                    };
                    debug!("PDF文本预览: {}", preview.replace('\n', " ").replace('\r', " "));

                    Ok(extracted_text)
                }
                Err(e) => {
                    // 记录详细的错误信息用于调试
                    let error_string = e.to_string();
                    error!("PDF文档加载失败 - 文件: {}", file_path.display());
                    error!("错误详情: {}", error_string);
                    error!("错误类型: {:?}", e);

                    // 尝试用不同的方法检查文件
                    debug!("尝试检查文件是否可读...");
                    match std::fs::File::open(&file_path) {
                        Ok(_) => debug!("文件可以正常打开"),
                        Err(file_err) => error!("文件无法打开: {}", file_err),
                    }

                    // 根据错误类型提供更具体的错误信息
                    let error_msg = if error_string.contains("password") || error_string.contains("encrypted") {
                        format!("PDF文档已加密，需要密码: {}", e)
                    } else if error_string.contains("corrupt") || error_string.contains("damaged") {
                        format!("PDF文档已损坏: {}", e)
                    } else if error_string.contains("permission") || error_string.contains("access") {
                        format!("PDF文档访问权限不足: {}", e)
                    } else if error_string.contains("format") || error_string.contains("invalid") {
                        format!("PDF文档格式无效: {}", e)
                    } else {
                        format!("PDF文档加载失败: {}", e)
                    };

                    Err(anyhow::anyhow!("{}", error_msg))
                }
            }
        }).await?;

        result
    }

    async fn extract_text_with_ocr(&self, file_path: &Path) -> Result<String> {
        if !self.config.enable_ocr {
            return Err(anyhow::anyhow!("OCR 功能已禁用"));
        }

        let file_path = file_path.to_owned();
        let config = self.config.clone();

        let result = task::spawn_blocking(move || {
            debug!("开始使用 OCR 提取文本: {}", file_path.display());

            // 创建临时输出文件
            let temp_dir = std::env::temp_dir();
            let temp_output = temp_dir.join(format!(
                "ocr_output_{}_{}.pdf",
                std::process::id(),
                std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_millis()
            ));

            // 设置 OCR 参数
            let mut ocr_args = vec![
                "--language".to_string(),
                config.ocr_languages.clone(),
                "--output-type".to_string(),
                "pdf".to_string(),
                "--force-ocr".to_string(), // 强制 OCR，即使已有文本
            ];

            // 如果设置了页数限制，添加相应参数
            if let Some(max_pages) = config.max_pages_per_document {
                ocr_args.push("--pages".to_string());
                ocr_args.push(format!("1-{}", max_pages));
            }

            debug!("OCR 参数: {:?}", ocr_args);

            // 创建 OCR 实例
            let mut ocr = OcrMyPdf::new(
                Some(ocr_args),
                Some(file_path.to_string_lossy().to_string()),
                Some(temp_output.to_string_lossy().to_string()),
            );

            // 执行 OCR
            debug!("执行 OCR 处理...");
            ocr.execute();

            // 检查输出文件是否存在
            if !temp_output.exists() {
                return Err(anyhow::anyhow!("OCR 处理失败，未生成输出文件"));
            }

            debug!("OCR 处理完成，开始从输出文件提取文本");

            // 从 OCR 后的 PDF 文件中提取文本
            let file_uri = format!("file://{}", temp_output.to_string_lossy());
            let document = Document::from_file(&file_uri, None)
                .with_context(|| "无法加载 OCR 后的 PDF 文档")?;

            let page_count = document.n_pages();
            let mut extracted_text = String::new();
            let mut successful_pages = 0;

            debug!("从 OCR 后的 PDF 提取文本: {} 页", page_count);

            for page_num in 0..page_count {
                if let Some(page) = document.page(page_num) {
                    if let Some(page_text) = page.text() {
                        let page_text_str = page_text.to_string();
                        if !page_text_str.trim().is_empty() {
                            extracted_text.push_str(&page_text_str);
                            extracted_text.push('\n');
                            successful_pages += 1;
                        }
                    }
                }
            }

            // 清理临时文件
            let _ = std::fs::remove_file(&temp_output);

            debug!("OCR 文本提取完成: {} 字符 (成功页面: {})",
                   extracted_text.chars().count(), successful_pages);

            if extracted_text.trim().is_empty() {
                Err(anyhow::anyhow!("OCR 未能提取到任何文本"))
            } else {
                Ok(extracted_text)
            }
        }).await?;

        result
    }



    fn collect_pdf_files(&self, directory: &Path) -> Result<Vec<PathBuf>> {
        let mut pdf_files = Vec::new();

        for entry in walkdir::WalkDir::new(directory)
            .follow_links(false)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            let path = entry.path();
            if path.is_file() {
                if let Some(extension) = path.extension() {
                    if extension.to_string_lossy().to_lowercase() == "pdf" {
                        pdf_files.push(path.to_owned());
                    }
                }
            }
        }

        Ok(pdf_files)
    }

    async fn is_already_processed(&self, file_path: &Path) -> Result<bool> {
        let path_str = file_path.to_string_lossy();

        let row = sqlx::query("SELECT COUNT(*) as count FROM pdf_languages WHERE file_path = ?")
            .bind(path_str.as_ref())
            .fetch_one(&self.db_pool)
            .await?;

        Ok(row.get::<i64, _>("count") > 0)
    }

    async fn save_batch_results(&self, results: Vec<Result<InternalDetectionResult>>) -> Result<()> {
        let mut tx = self.db_pool.begin().await?;

        for result in results {
            match result {
                Ok(detection) => {
                    sqlx::query(
                        r#"
                        INSERT OR REPLACE INTO pdf_languages
                        (file_path, primary_language, confidence, text_sample, file_size, processing_time_ms, error, detection_method, text_length, is_reliable, processed_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                        "#,
                    )
                        .bind(&detection.file_path)
                        .bind(&detection.primary_language)
                        .bind(detection.confidence)
                        .bind(&detection.text_sample)
                        .bind(detection.file_size as i64)
                        .bind(detection.processing_time_ms as i64)
                        .bind(&detection.error)
                        .bind(&detection.detection_method)
                        .bind(detection.text_length as i64)
                        .bind(detection.is_reliable)
                        .execute(&mut *tx)
                        .await?;
                }
                Err(e) => {
                    error!("处理文件时发生错误: {}", e);
                }
            }
        }

        tx.commit().await?;
        Ok(())
    }

    // 查询统计信息
    pub async fn get_language_statistics(&self) -> Result<HashMap<String, usize>> {
        let rows = sqlx::query("SELECT primary_language, COUNT(*) as count FROM pdf_languages WHERE error IS NULL GROUP BY primary_language")
            .fetch_all(&self.db_pool)
            .await?;

        let mut stats = HashMap::new();
        for row in rows {
            let lang: String = row.get("primary_language");
            let count: i64 = row.get("count");
            stats.insert(lang, count as usize);
        }

        Ok(stats)
    }

    // 导出结果到JSON
    pub async fn export_results_to_json(&self, output_path: &Path) -> Result<()> {
        let rows = sqlx::query("SELECT file_path, primary_language, confidence, text_sample, text_length, detection_method, processed_at, error FROM pdf_languages")
            .fetch_all(&self.db_pool)
            .await?;

        let mut detections = Vec::new();
        for row in rows {
            let file_path: String = row.get("file_path");
            let filename = Path::new(&file_path)
                .file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("unknown")
                .to_string();

            // 限制文本样本长度为200字符（安全截取，考虑UTF-8字符边界）
            let full_text_sample: String = row.get::<Option<String>, _>("text_sample").unwrap_or_default();
            let text_sample = if full_text_sample.chars().count() > 200 {
                let truncated: String = full_text_sample.chars().take(200).collect();
                format!("{}...", truncated)
            } else {
                full_text_sample
            };

            let detection = LanguageDetection {
                filename,
                language: row.get("primary_language"),
                confidence: row.get("confidence"),
                text_length: row.get::<Option<i64>, _>("text_length").unwrap_or(0) as usize,
                text_sample,
                detection_method: row.get::<Option<String>, _>("detection_method").unwrap_or("unknown".to_string()),
                processed_at: row.get::<Option<String>, _>("processed_at").unwrap_or("unknown".to_string()),
                error: row.get("error"),
            };
            detections.push(detection);
        }

        let json = serde_json::to_string_pretty(&detections)?;
        tokio::fs::write(output_path, json).await?;

        Ok(())
    }
}

impl Clone for PdfLanguageDetector {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            db_pool: self.db_pool.clone(),
            semaphore: Arc::clone(&self.semaphore),
        }
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 解析命令行参数
    let args = Args::parse();

    // 检查输入目录是否存在
    if !args.input_dir.exists() {
        return Err(anyhow::anyhow!("输入目录不存在: {}", args.input_dir.display()));
    }

    if !args.input_dir.is_dir() {
        return Err(anyhow::anyhow!("输入路径不是目录: {}", args.input_dir.display()));
    }

    // 配置参数
    let config = ProcessingConfig {
        max_concurrent_files: args.max_concurrent,
        max_text_extract_length: 5000, // 增加到5000字符
        min_reliable_text_length: 300,
        batch_size: args.batch_size,
        database_path: args.database,
        skip_processed: args.skip_processed,
        use_multiple_detectors: true,
        max_pages_per_document: Some(args.max_pages), // 使用命令行参数
        memory_limit_mb: args.memory_limit, // 使用命令行参数
        enable_ocr: args.enable_ocr, // 使用命令行参数
        ocr_languages: args.ocr_languages, // 使用命令行参数
    };

    info!("开始处理，配置参数:");
    info!("  输入目录: {}", args.input_dir.display());
    info!("  数据库路径: {}", config.database_path);
    info!("  最大并发数: {}", config.max_concurrent_files);
    info!("  批次大小: {}", config.batch_size);
    info!("  跳过已处理: {}", config.skip_processed);
    info!("  最大页数限制: {}", config.max_pages_per_document.unwrap_or(0));
    info!("  内存限制: {} MB", config.memory_limit_mb);
    info!("  启用OCR: {}", config.enable_ocr);
    if config.enable_ocr {
        info!("  OCR语言: {}", config.ocr_languages);
    }

    // 创建检测器
    let detector = PdfLanguageDetector::new(config).await?;

    // 处理PDF目录
    detector.process_directory(&args.input_dir).await?;

    // 输出详细统计信息
    let stats = detector.get_language_statistics().await?;
    println!("\n=== 语言分布统计 ===");
    let mut sorted_stats: Vec<_> = stats.iter().collect();
    sorted_stats.sort_by(|a, b| b.1.cmp(a.1)); // 按文件数量降序排列

    let total_files: usize = sorted_stats.iter().map(|(_, count)| *count).sum();
    println!("总文件数: {}", total_files);
    println!();

    for (lang_code, count) in sorted_stats.iter().take(10) { // 显示前10种语言
        let lang_name = get_language_name(lang_code);
        let percentage = (**count as f64 / total_files as f64) * 100.0;
        println!("{} ({}): {} 个文件 ({:.1}%)", lang_name, lang_code, count, percentage);
    }

    if sorted_stats.len() > 10 {
        let others: usize = sorted_stats.iter().skip(10).map(|(_, count)| *count).sum();
        let others_percentage = (others as f64 / total_files as f64) * 100.0;
        println!("其他 {} 种语言: {} 个文件 ({:.1}%)",
                 sorted_stats.len() - 10, others, others_percentage);
    }

    // 显示可靠性统计
    let reliable_query = sqlx::query("SELECT COUNT(*) as count FROM pdf_languages WHERE is_reliable = true AND error IS NULL")
        .fetch_one(&detector.db_pool).await?;
    let reliable_count: i64 = reliable_query.get("count");

    let unreliable_query = sqlx::query("SELECT COUNT(*) as count FROM pdf_languages WHERE is_reliable = false AND error IS NULL")
        .fetch_one(&detector.db_pool).await?;
    let unreliable_count: i64 = unreliable_query.get("count");

    println!("\n=== 检测统计 ===");
    println!("长文本检测(≥300字符): {} 个文件", reliable_count);
    println!("短文本检测(<300字符): {} 个文件", unreliable_count);
    if reliable_count + unreliable_count > 0 {
        let reliable_rate = reliable_count as f64 / (reliable_count + unreliable_count) as f64 * 100.0;
        println!("长文本占比: {:.1}%", reliable_rate);
    }

    // 导出结果（如果指定了路径）
    if let Some(json_path) = args.export_json {
        info!("导出结果到: {}", json_path.display());
        detector.export_results_to_json(&json_path).await?;
    }

    info!("处理完成!");
    Ok(())
}

// 多种语言检测方法的结果
#[derive(Debug, Clone)]
struct DetectionResult {
    language: String,
    confidence: f64,
    method: String,
    is_reliable: bool,
}

// 智能语言检测函数
fn detect_language_smart(text: &str) -> Option<DetectionResult> {
    let text_len = text.chars().count();
    debug!("检测文本长度: {} 字符", text_len);

    if text_len < 10 {
        warn!("文本太短，无法可靠检测语言");
        return None;
    }

    // 使用 whichlang 进行检测
    let lang = whichlang::detect_language(text);

    // whichlang 不提供置信度，根据文本长度判断可靠性
    let is_reliable = text_len >= 300;  // 长文本更可靠

    Some(DetectionResult {
        language: format!("{:?}", lang).to_lowercase(),  // 将 Lang 枚举转为字符串
        confidence: if is_reliable { 0.9 } else { 0.7 },  // 模拟置信度
        method: "whichlang".to_string(),
        is_reliable,
    })
}

fn safe_substring(text: &str, max_chars: usize) -> String {
    text.chars().take(max_chars).collect()
}

// 辅助函数：清理PDF提取的文本
fn clean_extracted_text(text: &str) -> String {
    let cleaned: String = text
        // 移除过多的空白字符
        .split_whitespace()
        .collect::<Vec<_>>()
        .join(" ")
        // 移除控制字符，但保留基本标点
        .chars()
        .filter(|c| !c.is_control() || c.is_whitespace())
        .collect();

    debug!("文本清理: 原始长度 {} -> 清理后长度 {}",
           text.chars().count(),
           cleaned.chars().count());

    cleaned
}

// 辅助函数：提取文本样本
fn extract_text_samples(text: &str, sample_size: usize, num_samples: usize) -> Vec<String> {
    let text_len = text.chars().count();
    if text_len <= sample_size {
        return vec![text.to_string()];
    }

    let mut samples = Vec::new();
    let step = if num_samples > 1 {
        (text_len - sample_size) / (num_samples - 1)
    } else {
        0
    };

    for i in 0..num_samples {
        let start = i * step;
        if start + sample_size <= text_len {
            let sample: String = text.chars().skip(start).take(sample_size).collect();
            samples.push(sample);
        }
    }

    if samples.is_empty() {
        samples.push(text.chars().take(sample_size).collect());
    }

    samples
}

// 辅助函数：获取语言的友好名称 (whichlang 语言码映射)
pub fn get_language_name(code: &str) -> String {
    match code {
        "cmn" | "chinese" | "zh" | "zh-cn" | "zh-tw" => "中文".to_string(),
        "eng" | "english" | "en" => "英文".to_string(),
        "jpn" | "japanese" | "ja" => "日文".to_string(),
        "kor" | "korean" | "ko" => "韩文".to_string(),
        "fra" | "french" | "fr" => "法文".to_string(),
        "deu" | "german" | "de" => "德文".to_string(),
        "spa" | "spanish" | "es" => "西班牙文".to_string(),
        "rus" | "russian" | "ru" => "俄文".to_string(),
        "ara" | "arabic" | "ar" => "阿拉伯文".to_string(),
        "hin" | "hindi" | "hi" => "印地文".to_string(),
        "portuguese" | "pt" => "葡萄牙文".to_string(),
        "italian" | "it" => "意大利文".to_string(),
        "dutch" | "nl" => "荷兰文".to_string(),
        "swedish" | "sv" => "瑞典文".to_string(),
        "danish" | "da" => "丹麦文".to_string(),
        "norwegian" | "no" => "挪威文".to_string(),
        "finnish" | "fi" => "芬兰文".to_string(),
        "turkish" | "tr" => "土耳其文".to_string(),
        "polish" | "pl" => "波兰文".to_string(),
        "czech" | "cs" => "捷克文".to_string(),
        "hungarian" | "hu" => "匈牙利文".to_string(),
        "romanian" | "ro" => "罗马尼亚文".to_string(),
        "bulgarian" | "bg" => "保加利亚文".to_string(),
        "croatian" | "hr" => "克罗地亚文".to_string(),
        "slovak" | "sk" => "斯洛伐克文".to_string(),
        "slovenian" | "sl" => "斯洛文尼亚文".to_string(),
        "estonian" | "et" => "爱沙尼亚文".to_string(),
        "latvian" | "lv" => "拉脱维亚文".to_string(),
        "lithuanian" | "lt" => "立陶宛文".to_string(),
        "ukrainian" | "uk" => "乌克兰文".to_string(),
        "belarusian" | "be" => "白俄罗斯文".to_string(),
        "macedonian" | "mk" => "马其顿文".to_string(),
        "albanian" | "sq" => "阿尔巴尼亚文".to_string(),
        "catalan" | "ca" => "加泰罗尼亚文".to_string(),
        "basque" | "eu" => "巴斯克文".to_string(),
        "galician" | "gl" => "加利西亚文".to_string(),
        "welsh" | "cy" => "威尔士文".to_string(),
        "irish" | "ga" => "爱尔兰文".to_string(),
        "icelandic" | "is" => "冰岛文".to_string(),
        "maltese" | "mt" => "马耳他文".to_string(),
        "thai" | "th" => "泰文".to_string(),
        "vietnamese" | "vi" => "越南文".to_string(),
        "malay" | "ms" => "马来文".to_string(),
        "indonesian" | "id" => "印尼文".to_string(),
        "tagalog" | "tl" => "菲律宾文".to_string(),
        "swahili" | "sw" => "斯瓦希里文".to_string(),
        "amharic" | "am" => "阿姆哈拉文".to_string(),
        "hebrew" | "he" => "希伯来文".to_string(),
        "persian" | "fa" => "波斯文".to_string(),
        "urdu" | "ur" => "乌尔都文".to_string(),
        "bengali" | "bn" => "孟加拉文".to_string(),
        "punjabi" | "pa" => "旁遮普文".to_string(),
        "gujarati" | "gu" => "古吉拉特文".to_string(),
        "oriya" | "or" => "奥里亚文".to_string(),
        "tamil" | "ta" => "泰米尔文".to_string(),
        "telugu" | "te" => "泰卢固文".to_string(),
        "kannada" | "kn" => "卡纳达文".to_string(),
        "malayalam" | "ml" => "马拉雅拉姆文".to_string(),
        "sinhala" | "si" => "僧伽罗文".to_string(),
        "myanmar" | "my" => "缅甸文".to_string(),
        "khmer" | "km" => "柬埔寨文".to_string(),
        "lao" | "lo" => "老挝文".to_string(),
        "georgian" | "ka" => "格鲁吉亚文".to_string(),
        "armenian" | "hy" => "亚美尼亚文".to_string(),
        "azerbaijani" | "az" => "阿塞拜疆文".to_string(),
        "kazakh" | "kk" => "哈萨克文".to_string(),
        "kyrgyz" | "ky" => "吉尔吉斯文".to_string(),
        "uzbek" | "uz" => "乌兹别克文".to_string(),
        "turkmen" | "tk" => "土库曼文".to_string(),
        "tajik" | "tg" => "塔吉克文".to_string(),
        "mongolian" | "mn" => "蒙古文".to_string(),
        // whichlang 特有的语言码
        "afrikaans" | "af" => "南非荷兰文".to_string(),
        "bosnian" | "bs" => "波斯尼亚文".to_string(),
        "esperanto" | "eo" => "世界语".to_string(),
        "faroese" | "fo" => "法罗文".to_string(),
        "frisian" | "fy" => "弗里西亚文".to_string(),
        "gaelic" | "gd" => "苏格兰盖尔文".to_string(),
        "luxembourgish" | "lb" => "卢森堡文".to_string(),
        "maori" | "mi" => "毛利文".to_string(),
        "somali" | "so" => "索马里文".to_string(),
        "xhosa" | "xh" => "科萨文".to_string(),
        "zulu" | "zu" => "祖鲁文".to_string(),
        _ => format!("未知语言({})", code),
    }
}