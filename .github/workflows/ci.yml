name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  CARGO_TERM_COLOR: always

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libpoppler-glib-dev \
            libcairo2-dev \
            libpango1.0-dev \
            libgdk-pixbuf2.0-dev \
            libglib2.0-dev

      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2

      - name: Run tests
        run: cargo test --verbose

      - name: Run clippy
        run: cargo clippy -- -D warnings

      - name: Check formatting
        run: cargo fmt -- --check

  build:
    name: Build
    needs: test
    strategy:
      matrix:
        include:
          - target: x86_64-unknown-linux-gnu
            os: ubuntu-latest
            name: linux-x86_64
          - target: aarch64-unknown-linux-gnu
            os: ubuntu-latest
            name: linux-arm64
          - target: x86_64-apple-darwin
            os: macos-latest
            name: macos-x86_64
          - target: aarch64-apple-darwin
            os: macos-latest
            name: macos-arm64
          - target: x86_64-pc-windows-msvc
            os: windows-latest
            name: windows-x86_64

    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Install system dependencies (Ubuntu)
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libpoppler-glib-dev \
            libcairo2-dev \
            libpango1.0-dev \
            libgdk-pixbuf2.0-dev \
            libglib2.0-dev

      - name: Install cross-compilation tools (Ubuntu ARM64)
        if: matrix.target == 'aarch64-unknown-linux-gnu'
        run: |
          sudo apt-get install -y gcc-aarch64-linux-gnu
          echo "CARGO_TARGET_AARCH64_UNKNOWN_LINUX_GNU_LINKER=aarch64-linux-gnu-gcc" >> $GITHUB_ENV

      - name: Install system dependencies (macOS)
        if: matrix.os == 'macos-latest'
        run: |
          brew install poppler cairo pango gdk-pixbuf glib

      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2
        with:
          key: ${{ matrix.target }}

      - name: Build binary
        run: cargo build --release --target ${{ matrix.target }}

      - name: Package binary (Unix)
        if: matrix.os != 'windows-latest'
        run: |
          mkdir -p dist
          cp target/${{ matrix.target }}/release/rust-pdf-lang-detection dist/
          cp README.md QUICKSTART.md LICENSE* dist/ 2>/dev/null || true
          cd dist
          tar -czf ../rust-pdf-lang-detection-${{ matrix.name }}.tar.gz *

      - name: Package binary (Windows)
        if: matrix.os == 'windows-latest'
        run: |
          mkdir dist
          cp target/${{ matrix.target }}/release/rust-pdf-lang-detection.exe dist/
          cp README.md QUICKSTART.md dist/
          if (Test-Path "LICENSE*") { cp LICENSE* dist/ }
          cd dist
          Compress-Archive -Path * -DestinationPath ../rust-pdf-lang-detection-${{ matrix.name }}.zip

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: rust-pdf-lang-detection-${{ matrix.name }}
          path: rust-pdf-lang-detection-${{ matrix.name }}.*

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Install cargo-audit
        run: cargo install cargo-audit

      - name: Run security audit
        run: cargo audit

  coverage:
    name: Code Coverage
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          components: llvm-tools-preview

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libpoppler-glib-dev \
            libcairo2-dev \
            libpango1.0-dev \
            libgdk-pixbuf2.0-dev \
            libglib2.0-dev

      - name: Install cargo-llvm-cov
        run: cargo install cargo-llvm-cov

      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2

      - name: Generate code coverage
        run: cargo llvm-cov --all-features --workspace --lcov --output-path lcov.info

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          files: lcov.info
          fail_ci_if_error: true
