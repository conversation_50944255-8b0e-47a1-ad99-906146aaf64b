name: Release

permissions:
  contents: write
  issues: write
  pull-requests: write

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag to release'
        required: true
        default: 'v0.1.0'

env:
  CARGO_TERM_COLOR: always

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      release_id: ${{ steps.create_release.outputs.id }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Get tag name
        id: tag_name
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "TAG_NAME=${{ github.event.inputs.tag }}" >> $GITHUB_OUTPUT
          else
            echo "TAG_NAME=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          fi

      - name: Extract release notes
        id: extract_notes
        run: |
          if [ -f "CHANGELOG.md" ]; then
            # Extract notes for this version from CHANGELOG.md
            awk '/^## \[${{ steps.tag_name.outputs.TAG_NAME }}\]/{flag=1; next} /^## \[/{flag=0} flag' CHANGELOG.md > release_notes.md
          else
            echo "Release ${{ steps.tag_name.outputs.TAG_NAME }}" > release_notes.md
            echo "" >> release_notes.md
            echo "### Changes" >> release_notes.md
            echo "- See commit history for detailed changes" >> release_notes.md
          fi

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.tag_name.outputs.TAG_NAME }}
          release_name: Release ${{ steps.tag_name.outputs.TAG_NAME }}
          body_path: release_notes.md
          draft: false
          prerelease: false

  build-and-upload:
    name: Build and Upload
    needs: create-release
    strategy:
      matrix:
        include:
          - target: x86_64-unknown-linux-musl
            os: ubuntu-latest
            name: linux-x86_64
            asset_name: rust-pdf-lang-detection-linux-x86_64.tar.gz
          - target: aarch64-apple-darwin
            os: macos-latest
            name: macos-arm64
            asset_name: rust-pdf-lang-detection-macos-arm64.tar.gz
            
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Install system dependencies (Ubuntu)
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libpoppler-glib-dev \
            libcairo2-dev \
            libpango1.0-dev \
            libgdk-pixbuf2.0-dev \
            libglib2.0-dev

      - name: Install system dependencies (macOS)
        if: matrix.os == 'macos-latest'
        run: |
          brew install poppler cairo pango gdk-pixbuf glib

      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2
        with:
          key: ${{ matrix.target }}

      - name: Build binary
        run: cargo build --release --target ${{ matrix.target }}

      - name: Package binary (Unix)
        if: matrix.os != 'windows-latest'
        run: |
          mkdir -p dist
          cp target/${{ matrix.target }}/release/rust-pdf-lang-detection dist/
          cp README.md QUICKSTART.md BINARY_RELEASES.md dist/
          cp install.sh test-installation.sh dist/
          cp -r examples dist/ 2>/dev/null || true
          if [ -f LICENSE ]; then cp LICENSE dist/; fi
          if [ -f LICENSE.md ]; then cp LICENSE.md dist/; fi
          cd dist
          tar -czf ../${{ matrix.asset_name }} *

      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ matrix.asset_name }}
          asset_name: ${{ matrix.asset_name }}
          asset_content_type: application/octet-stream

  update-install-script:
    name: Update Install Script URLs
    needs: [create-release, build-and-upload]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Get tag name
        id: tag_name
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "TAG_NAME=${{ github.event.inputs.tag }}" >> $GITHUB_OUTPUT
          else
            echo "TAG_NAME=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          fi

      - name: Update install script with release URLs
        run: |
          # Update the install script to use the actual repository URL
          sed -i 's|https://github.com/CheinTian/rust-pdf-lang-detector|https://github.com/${{ github.repository }}|g' install.sh
          sed -i 's|https://github.com/CheinTian/rust-pdf-lang-detector|https://github.com/${{ github.repository }}|g' README.md
          sed -i 's|https://github.com/CheinTian/rust-pdf-lang-detector|https://github.com/${{ github.repository }}|g' QUICKSTART.md
          sed -i 's|https://github.com/CheinTian/rust-pdf-lang-detector|https://github.com/${{ github.repository }}|g' BINARY_RELEASES.md

      - name: Commit and push changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add install.sh README.md QUICKSTART.md BINARY_RELEASES.md
          git diff --staged --quiet || git commit -m "Update repository URLs in documentation"
          git push

  notify:
    name: Notify Release
    needs: [create-release, build-and-upload]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Get tag name
        id: tag_name
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "TAG_NAME=${{ github.event.inputs.tag }}" >> $GITHUB_OUTPUT
          else
            echo "TAG_NAME=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          fi

      - name: Notify success
        if: needs.build-and-upload.result == 'success'
        run: |
          echo "✅ Release ${{ steps.tag_name.outputs.TAG_NAME }} completed successfully!"
          echo "📦 Binaries are available at: https://github.com/${{ github.repository }}/releases/tag/${{ steps.tag_name.outputs.TAG_NAME }}"

      - name: Notify failure
        if: needs.build-and-upload.result == 'failure'
        run: |
          echo "❌ Release ${{ steps.tag_name.outputs.TAG_NAME }} failed!"
          exit 1
