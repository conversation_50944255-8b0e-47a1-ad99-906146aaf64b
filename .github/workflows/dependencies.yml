name: Dependencies

on:
  schedule:
    # 每周一检查依赖更新
    - cron: '0 0 * * 1'
  workflow_dispatch:

jobs:
  check-dependencies:
    name: Check Dependencies
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Install cargo-outdated
        run: cargo install cargo-outdated

      - name: Install cargo-audit
        run: cargo install cargo-audit

      - name: Install cargo-deny
        run: cargo install cargo-deny

      - name: Check for outdated dependencies
        run: |
          echo "## Outdated Dependencies" >> dependency_report.md
          echo "\`\`\`" >> dependency_report.md
          cargo outdated >> dependency_report.md || echo "No outdated dependencies found" >> dependency_report.md
          echo "\`\`\`" >> dependency_report.md
          echo "" >> dependency_report.md

      - name: Security audit
        run: |
          echo "## Security Audit" >> dependency_report.md
          echo "\`\`\`" >> dependency_report.md
          cargo audit >> dependency_report.md || echo "No security vulnerabilities found" >> dependency_report.md
          echo "\`\`\`" >> dependency_report.md
          echo "" >> dependency_report.md

      - name: License check
        run: |
          echo "## License Check" >> dependency_report.md
          echo "\`\`\`" >> dependency_report.md
          cargo deny check licenses >> dependency_report.md || echo "License check passed" >> dependency_report.md
          echo "\`\`\`" >> dependency_report.md

      - name: Create Issue
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('dependency_report.md', 'utf8');
            
            const title = `Dependency Report - ${new Date().toISOString().split('T')[0]}`;
            const body = `# Weekly Dependency Report\n\n${report}\n\n---\n*This report was automatically generated by GitHub Actions.*`;
            
            // Check if there's already an open issue for this week
            const issues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              labels: ['dependencies', 'automated']
            });
            
            const existingIssue = issues.data.find(issue => 
              issue.title.includes(new Date().toISOString().split('T')[0])
            );
            
            if (!existingIssue) {
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: title,
                body: body,
                labels: ['dependencies', 'automated']
              });
            }

  update-dependencies:
    name: Update Dependencies
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Install cargo-edit
        run: cargo install cargo-edit

      - name: Update dependencies
        run: |
          cargo update
          cargo upgrade --workspace

      - name: Run tests
        run: cargo test

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update dependencies'
          title: 'chore: update dependencies'
          body: |
            ## Dependency Updates
            
            This PR updates project dependencies to their latest compatible versions.
            
            ### Changes
            - Updated Cargo.lock with latest dependency versions
            - All tests pass with updated dependencies
            
            ### Testing
            - [x] All existing tests pass
            - [x] No breaking changes detected
            
            ---
            *This PR was automatically created by GitHub Actions.*
          branch: update-dependencies
          delete-branch: true
          labels: |
            dependencies
            automated
