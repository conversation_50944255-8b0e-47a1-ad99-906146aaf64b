name: Benchmark

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  schedule:
    # 每天运行基准测试
    - cron: '0 2 * * *'

jobs:
  benchmark:
    name: Performance Benchmark
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libpoppler-glib-dev \
            libcairo2-dev \
            libpango1.0-dev \
            libgdk-pixbuf2.0-dev \
            libglib2.0-dev \
            ocrmypdf \
            tesseract-ocr

      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2

      - name: Build release binary
        run: cargo build --release

      - name: Create test PDFs
        run: |
          mkdir -p test-data
          # 创建一些测试 PDF 文件（这里使用占位符，实际应该有真实的测试文件）
          echo "Creating test PDF files..."
          # 在实际使用中，您应该添加一些标准的测试 PDF 文件

      - name: Run benchmark - Small files
        run: |
          echo "## Small Files Benchmark" >> benchmark_results.md
          echo "Testing with small PDF files (< 1MB)" >> benchmark_results.md
          echo "\`\`\`" >> benchmark_results.md
          time ./target/release/rust-pdf-lang-detection \
            --input-dir ./test-data \
            --max-concurrent 1 \
            --batch-size 1 \
            --enable-ocr false \
            --export-json small_files_results.json 2>&1 | tee -a benchmark_results.md || true
          echo "\`\`\`" >> benchmark_results.md
          echo "" >> benchmark_results.md

      - name: Run benchmark - With OCR
        run: |
          echo "## OCR Benchmark" >> benchmark_results.md
          echo "Testing with OCR enabled" >> benchmark_results.md
          echo "\`\`\`" >> benchmark_results.md
          time ./target/release/rust-pdf-lang-detection \
            --input-dir ./test-data \
            --max-concurrent 1 \
            --batch-size 1 \
            --enable-ocr true \
            --ocr-languages eng \
            --export-json ocr_results.json 2>&1 | tee -a benchmark_results.md || true
          echo "\`\`\`" >> benchmark_results.md
          echo "" >> benchmark_results.md

      - name: Run benchmark - Concurrent processing
        run: |
          echo "## Concurrent Processing Benchmark" >> benchmark_results.md
          echo "Testing with multiple concurrent workers" >> benchmark_results.md
          echo "\`\`\`" >> benchmark_results.md
          time ./target/release/rust-pdf-lang-detection \
            --input-dir ./test-data \
            --max-concurrent 4 \
            --batch-size 10 \
            --enable-ocr false \
            --export-json concurrent_results.json 2>&1 | tee -a benchmark_results.md || true
          echo "\`\`\`" >> benchmark_results.md
          echo "" >> benchmark_results.md

      - name: Memory usage test
        run: |
          echo "## Memory Usage Test" >> benchmark_results.md
          echo "Testing memory consumption" >> benchmark_results.md
          echo "\`\`\`" >> benchmark_results.md
          /usr/bin/time -v ./target/release/rust-pdf-lang-detection \
            --input-dir ./test-data \
            --memory-limit 128 \
            --export-json memory_test_results.json 2>&1 | tee -a benchmark_results.md || true
          echo "\`\`\`" >> benchmark_results.md

      - name: Generate performance report
        run: |
          echo "# Performance Benchmark Report" > performance_report.md
          echo "Date: $(date)" >> performance_report.md
          echo "Commit: ${{ github.sha }}" >> performance_report.md
          echo "" >> performance_report.md
          cat benchmark_results.md >> performance_report.md

      - name: Upload benchmark results
        uses: actions/upload-artifact@v4
        with:
          name: benchmark-results-${{ github.sha }}
          path: |
            performance_report.md
            *_results.json

      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('performance_report.md', 'utf8');
            
            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 📊 Performance Benchmark Results\n\n${report}`
            });

  cargo-bench:
    name: Cargo Benchmark
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libpoppler-glib-dev \
            libcairo2-dev \
            libpango1.0-dev \
            libgdk-pixbuf2.0-dev \
            libglib2.0-dev

      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2

      - name: Run cargo bench
        run: |
          # 如果项目中有 benches/ 目录，运行基准测试
          if [ -d "benches" ]; then
            cargo bench
          else
            echo "No benchmark tests found"
          fi

  size-analysis:
    name: Binary Size Analysis
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libpoppler-glib-dev \
            libcairo2-dev \
            libpango1.0-dev \
            libgdk-pixbuf2.0-dev \
            libglib2.0-dev

      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2

      - name: Build release binary
        run: cargo build --release

      - name: Analyze binary size
        run: |
          echo "# Binary Size Analysis" > size_report.md
          echo "Date: $(date)" >> size_report.md
          echo "Commit: ${{ github.sha }}" >> size_report.md
          echo "" >> size_report.md
          echo "## Binary Size" >> size_report.md
          echo "\`\`\`" >> size_report.md
          ls -lh target/release/rust-pdf-lang-detection >> size_report.md
          echo "\`\`\`" >> size_report.md
          echo "" >> size_report.md
          echo "## Stripped Binary Size" >> size_report.md
          echo "\`\`\`" >> size_report.md
          strip target/release/rust-pdf-lang-detection
          ls -lh target/release/rust-pdf-lang-detection >> size_report.md
          echo "\`\`\`" >> size_report.md

      - name: Upload size analysis
        uses: actions/upload-artifact@v4
        with:
          name: size-analysis-${{ github.sha }}
          path: size_report.md
